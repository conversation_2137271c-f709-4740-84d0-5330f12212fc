#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
陶瓷行业新闻爬虫脚本
目标网站：http://www.fcia.com.cn/news/
爬取ID为3、4、5的新闻分类的所有文章内容

功能特性：
- 自动遍历所有分页
- 提取文章完整内容
- 智能文件名处理
- 进度实时显示
- 错误处理和重试机制

作者：AI Assistant
创建时间：2025-08-05
"""

import os
import re
import time
import random
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CeramicNewsCrawler:
    """陶瓷新闻爬虫类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "http://www.fcia.com.cn"
        self.target_urls = [
            "http://www.fcia.com.cn/news/?id=3",
            "http://www.fcia.com.cn/news/?id=4", 
            "http://www.fcia.com.cn/news/?id=5"
        ]
        self.output_dir = "结果"
        self.session = requests.Session()
        self.setup_session()
        self.stats = {
            'total_articles': 0,
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0
        }
        
    def setup_session(self):
        """配置请求会话"""
        # Source: context7-mcp on 'requests session management'
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(headers)
        self.session.timeout = 30
        
    def create_output_directory(self):
        """创建输出目录"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建输出目录: {self.output_dir}")
        else:
            logger.info(f"输出目录已存在: {self.output_dir}")
            
    def sanitize_filename(self, filename):
        """处理文件名中的特殊字符，确保文件名合法"""
        # 移除或替换Windows文件名中的非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        filename = re.sub(illegal_chars, '_', filename)
        
        # 移除前后空格和点号
        filename = filename.strip(' .')
        
        # 限制文件名长度（Windows路径限制）
        if len(filename) > 200:
            filename = filename[:200]
            
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "未命名文章"
            
        return filename
        
    def safe_request(self, url, method='GET', data=None, max_retries=3):
        """安全的HTTP请求，包含重试机制，支持GET和POST方法"""
        for attempt in range(max_retries):
            try:
                # 添加随机延迟，避免对服务器造成压力
                time.sleep(random.uniform(1, 3))

                if method.upper() == 'POST':
                    response = self.session.post(url, data=data)
                else:
                    response = self.session.get(url)

                response.raise_for_status()
                response.encoding = 'utf-8'  # 确保正确的编码
                return response

            except requests.exceptions.RequestException as e:
                logger.warning(f"{method}请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"{method}请求最终失败: {url}")
                    return None
                time.sleep(random.uniform(2, 5))  # 失败后等待更长时间

        return None
        
    def parse_article_list(self, html_content):
        """解析文章列表页面，提取文章链接"""
        # Source: context7-mcp on 'BeautifulSoup HTML parsing'
        soup = BeautifulSoup(html_content, 'html.parser')
        article_links = []

        # 使用精确的CSS选择器定位文章列表
        # 根据实际HTML结构：ul.com_news_list li a
        article_list_items = soup.select('ul.com_news_list li')

        for item in article_list_items:
            # 提取文章链接
            link = item.select_one('a[href*="/detail/"]')
            if link:
                href = link.get('href')
                if href:
                    # 转换为绝对URL
                    full_url = urljoin(self.base_url, href)

                    # 从h2标签提取文章标题
                    title_elem = item.select_one('h2')
                    title = title_elem.get_text(strip=True) if title_elem else link.get_text(strip=True)

                    # 确保标题和URL有效，且避免重复
                    if title and full_url not in [item['url'] for item in article_links]:
                        article_links.append({
                            'url': full_url,
                            'title': title
                        })
                        logger.debug(f"找到文章: {title[:30]}... -> {full_url}")

        logger.info(f"在当前页面找到 {len(article_links)} 篇文章")
        return article_links
        
    def get_all_page_numbers(self, html_content):
        """获取所有可用的页码"""
        soup = BeautifulSoup(html_content, 'html.parser')
        page_numbers = []

        # 根据实际HTML结构：.paging_nav a.pages_num
        page_links = soup.select('.paging_nav a.pages_num')

        for link in page_links:
            page_text = link.get_text(strip=True)
            try:
                page_num = int(page_text)
                if page_num not in page_numbers:
                    page_numbers.append(page_num)
            except ValueError:
                continue

        # 排序页码
        page_numbers.sort()
        logger.debug(f"找到页码: {page_numbers}")
        return page_numbers

    def has_next_page(self, current_page, all_pages):
        """检查是否还有下一页"""
        return current_page < max(all_pages) if all_pages else False
        
    def request_page_by_post(self, base_url, page_number):
        """通过POST请求获取指定页码的内容"""
        # 模拟表单提交，发送pages参数
        form_data = {'pages': str(page_number)}
        logger.debug(f"POST请求页码 {page_number} 到 {base_url}")
        return self.safe_request(base_url, method='POST', data=form_data)
        
    def extract_article_content(self, url):
        """提取文章详细内容"""
        response = self.safe_request(url)
        if not response:
            return None, None
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取文章标题
        title_selectors = [
            'h1', 'h2', '.article-title', '.news-title', 
            '.title', '.content-title', 'title'
        ]
        
        title = None
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 5:  # 确保标题有意义
                    break
                    
        # 提取文章内容
        content_selectors = [
            '.article-content', '.news-content', '.content',
            '.article-body', '.news-body', '.main-content',
            '#content', '#article', '.post-content'
        ]
        
        content = None
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式标签
                for script in content_elem(["script", "style"]):
                    script.decompose()
                content = content_elem.get_text(separator='\n', strip=True)
                if content and len(content) > 100:  # 确保内容有意义
                    break
                    
        # 如果没有找到专门的内容区域，尝试提取body中的文本
        if not content:
            body = soup.find('body')
            if body:
                for script in body(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                content = body.get_text(separator='\n', strip=True)
                
        return title, content
        
    def save_article(self, title, content, url):
        """保存文章到文件"""
        if not title or not content:
            logger.warning(f"文章标题或内容为空，跳过保存: {url}")
            self.stats['failed_count'] += 1
            return False
            
        # 处理文件名
        filename = self.sanitize_filename(title) + ".txt"
        filepath = os.path.join(self.output_dir, filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            logger.info(f"文件已存在，跳过: {filename}")
            self.stats['skipped_count'] += 1
            return True
            
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"标题: {title}\n")
                f.write(f"来源: {url}\n")
                f.write(f"爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                f.write(content)
                
            logger.info(f"成功保存文章: {filename}")
            self.stats['success_count'] += 1
            return True
            
        except Exception as e:
            logger.error(f"保存文章失败: {filename} - {str(e)}")
            self.stats['failed_count'] += 1
            return False
            
    def crawl_category(self, category_url):
        """爬取指定分类的所有文章"""
        logger.info(f"开始爬取分类: {category_url}")

        # 获取第一页，解析所有可用页码
        response = self.safe_request(category_url)
        if not response:
            logger.error(f"无法获取首页: {category_url}")
            return

        # 获取所有页码
        all_pages = self.get_all_page_numbers(response.text)
        if not all_pages:
            # 如果没有找到分页，只处理当前页
            all_pages = [1]

        logger.info(f"发现 {len(all_pages)} 页内容: {all_pages}")

        # 遍历所有页码
        for page_num in all_pages:
            logger.info(f"正在处理第 {page_num} 页")

            # 获取页面内容
            if page_num == 1:
                # 第一页使用已获取的响应
                page_response = response
            else:
                # 其他页面通过POST请求获取
                page_response = self.request_page_by_post(category_url, page_num)

            if not page_response:
                logger.error(f"无法获取第 {page_num} 页")
                continue

            # 解析文章列表
            article_links = self.parse_article_list(page_response.text)

            if not article_links:
                logger.warning(f"第 {page_num} 页没有找到文章链接")
                continue

            # 处理每篇文章
            for i, article in enumerate(article_links, 1):
                logger.info(f"处理文章 {i}/{len(article_links)}: {article['title'][:50]}...")

                title, content = self.extract_article_content(article['url'])
                if title and content:
                    self.save_article(title, content, article['url'])
                else:
                    logger.warning(f"无法提取文章内容: {article['url']}")
                    self.stats['failed_count'] += 1

                self.stats['total_articles'] += 1

        logger.info(f"分类 {category_url} 爬取完成，共处理 {len(all_pages)} 页")
                
    def run(self):
        """运行爬虫"""
        logger.info("=" * 60)
        logger.info("陶瓷行业新闻爬虫启动")
        logger.info("=" * 60)
        
        # 创建输出目录
        self.create_output_directory()
        
        # 爬取每个分类
        for i, url in enumerate(self.target_urls, 1):
            logger.info(f"\n开始爬取第 {i}/{len(self.target_urls)} 个分类")
            self.crawl_category(url)
            
        # 输出统计结果
        logger.info("\n" + "=" * 60)
        logger.info("爬取任务完成！")
        logger.info("=" * 60)
        logger.info(f"总文章数: {self.stats['total_articles']}")
        logger.info(f"成功保存: {self.stats['success_count']}")
        logger.info(f"跳过重复: {self.stats['skipped_count']}")
        logger.info(f"失败数量: {self.stats['failed_count']}")
        logger.info(f"成功率: {self.stats['success_count']/(self.stats['total_articles'] or 1)*100:.1f}%")
        logger.info(f"文章保存位置: {os.path.abspath(self.output_dir)}")

if __name__ == "__main__":
    crawler = CeramicNewsCrawler()
    crawler.run()
